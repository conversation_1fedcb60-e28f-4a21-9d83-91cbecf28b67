'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { companies } from '@/lib/data/companies';

import { BokehBackground, GlassCard } from '@/components/ui/aceternity/bokeh-background';
import { Breadcrumb, breadcrumbConfigs } from '@/components/ui/breadcrumb';
import { Badge } from '@/components/ui/badge';
import { AuthButton } from '@/components/ui/auth-button';
import { Building2, Users, Target, Calendar, ExternalLink, BookOpen, TrendingUp, ArrowRight, Rocket, Star, Zap } from 'lucide-react';
import { StarsBackground } from '@/components/ui/stars-background';
import { ShootingStars } from '@/components/ui/shooting-stars';


export default function SuccessStoriesPage() {
  return (
    <BokehBackground
        className="min-h-screen"
        density={60}
        speed={1.5}
        colors={['#9333ea', '#7c3aed', '#6366f1', '#8b5cf6', '#a855f7']}
      >
        <StarsBackground
          starDensity={0.0002}
          allStarsTwinkle={true}
          twinkleProbability={0.8}
          minTwinkleSpeed={0.5}
          maxTwinkleSpeed={1.2}
        />
        <ShootingStars
          minSpeed={8}
          maxSpeed={25}
          minDelay={1500}
          maxDelay={4000}
          starColor="#9333ea"
          trailColor="#7c3aed"
        />

        <div className="container mx-auto px-4 py-8 max-w-7xl relative z-20">
        {/* Breadcrumb */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Breadcrumb items={breadcrumbConfigs.successStories()} />
        </motion.div>

        {/* Enhanced Cosmic Header */}
        <motion.div
          className="text-center mb-20 relative"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Floating cosmic elements */}
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: 8 }).map((_, i) => (
              <motion.div
                key={`cosmic-element-${i}`}
                className="absolute w-1 h-1 bg-purple-400 rounded-full"
                style={{
                  left: `${20 + Math.random() * 60}%`,
                  top: `${20 + Math.random() * 60}%`,
                }}
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0, 1.5, 0],
                  rotate: [0, 180, 360],
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          <motion.div
            className="inline-flex items-center gap-2 rounded-full bg-purple-500/10 backdrop-blur-md border border-purple-500/20 px-6 py-3 text-sm font-medium text-purple-300 mb-8"
            initial={{ scale: 0, rotate: -10 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 0.6, delay: 0.3, type: "spring", bounce: 0.4 }}
          >
            <Star className="w-4 h-4" />
            Success Stories
            <Rocket className="w-4 h-4" />
          </motion.div>

          <motion.h1
            className="text-5xl lg:text-7xl font-bold text-white mb-8 tracking-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            Cosmic Journey to{' '}
            <motion.span
              className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent"
              initial={{ backgroundPosition: '0% 50%' }}
              animate={{ backgroundPosition: '100% 50%' }}
              transition={{ duration: 3, repeat: Infinity, repeatType: 'reverse' }}
            >
              Success
            </motion.span>
          </motion.h1>

          <motion.p
            className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
          >
            Witness the extraordinary transformation of visionary ideas into thriving enterprises.
            Our incubated startups have journeyed through the cosmos of innovation, emerging as
            stellar successes that illuminate the future of technology and business.
          </motion.p>
        </motion.div>

        {/* Cosmic Stats Overview */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
        >
          {[
            {
              value: companies.length,
              label: 'Portfolio Companies',
              color: 'text-purple-400',
              icon: <Building2 className="w-6 h-6 mb-2 text-purple-400" />
            },
            {
              value: `${Math.round(companies.reduce((acc, c) => acc + c.progress.overall, 0) / companies.length)}%`,
              label: 'Avg Progress',
              color: 'text-emerald-400',
              icon: <TrendingUp className="w-6 h-6 mb-2 text-emerald-400" />
            },
            {
              value: companies.reduce((acc, c) => acc + c.mentors.length, 0),
              label: 'Total Mentors',
              color: 'text-blue-400',
              icon: <Users className="w-6 h-6 mb-2 text-blue-400" />
            },
            {
              value: new Set(companies.map(c => c.industry)).size,
              label: 'Industries',
              color: 'text-pink-400',
              icon: <Target className="w-6 h-6 mb-2 text-pink-400" />
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.1 + index * 0.1 }}
            >
              <GlassCard className="p-6 text-center group hover:scale-105 transition-transform duration-300">
                <div className="flex flex-col items-center">
                  {stat.icon}
                  <motion.div
                    className={`text-3xl font-bold ${stat.color} mb-2`}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 1.3 + index * 0.1, type: "spring", bounce: 0.6 }}
                  >
                    {stat.value}
                  </motion.div>
                  <div className="text-sm text-gray-300 font-medium">{stat.label}</div>
                </div>
              </GlassCard>
            </motion.div>
          ))}
        </motion.div>

        {/* Cosmic Companies Showcase */}
        <div className="space-y-16">
          {companies.map((company, index) => (
            <motion.div
              key={company.id}
              className="relative"
              initial={{ opacity: 0, x: index % 2 === 0 ? -100 : 100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true, margin: "-100px" }}
            >
              {/* Floating background elements */}
              <div className="absolute inset-0 pointer-events-none overflow-hidden">
                {Array.from({ length: 4 }).map((_, i) => (
                  <motion.div
                    key={`floating-${company.id}-${i}`}
                    className="absolute w-2 h-2 bg-purple-400/20 rounded-full"
                    style={{
                      left: `${10 + Math.random() * 80}%`,
                      top: `${10 + Math.random() * 80}%`,
                    }}
                    animate={{
                      y: [-15, 15, -15],
                      x: [-10, 10, -10],
                      opacity: [0.2, 0.6, 0.2],
                      scale: [0.8, 1.4, 0.8],
                    }}
                    transition={{
                      duration: 5 + Math.random() * 3,
                      repeat: Infinity,
                      delay: Math.random() * 2,
                      ease: "easeInOut"
                    }}
                  />
                ))}
              </div>

              <div className={`flex flex-col lg:flex-row items-center gap-12 ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''}`}>
                {/* Company Visual */}
                <motion.div
                  className="flex-1 max-w-lg"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                >
                  <GlassCard className="p-8 group relative overflow-hidden">
                    {/* Gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                    <div className="relative z-10">
                      <div className="text-center mb-6">
                        <motion.div
                          className="w-20 h-20 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 border border-purple-500/30 flex items-center justify-center overflow-hidden"
                          whileHover={{ rotate: 5, scale: 1.1 }}
                          transition={{ duration: 0.3 }}
                        >
                          {company.logo ? (
                            <Image
                              src={company.logo}
                              alt={`${company.name} logo`}
                              width={56}
                              height={56}
                              className="object-contain"
                            />
                          ) : (
                            <Building2 className="h-10 w-10 text-purple-400" />
                          )}
                        </motion.div>

                        <Badge variant="secondary" className="bg-purple-500/20 text-purple-300 border-purple-500/30 mb-4">
                          {company.industry}
                        </Badge>
                      </div>

                      {/* Progress Ring */}
                      <div className="relative w-24 h-24 mx-auto mb-6">
                        <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                          <path
                            className="text-gray-700"
                            stroke="currentColor"
                            strokeWidth="2"
                            fill="none"
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          />
                          <motion.path
                            className="text-purple-400"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            fill="none"
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                            initial={{ strokeDasharray: "0 100" }}
                            whileInView={{ strokeDasharray: `${company.progress.overall} 100` }}
                            transition={{ duration: 1.5, delay: 0.5 }}
                          />
                        </svg>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <span className="text-lg font-bold text-white">{company.progress.overall}%</span>
                        </div>
                      </div>

                      {/* Stats Grid */}
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div className="space-y-1">
                          <Users className="w-4 h-4 text-blue-400 mx-auto" />
                          <div className="text-lg font-bold text-blue-400">{company.mentors.length}</div>
                          <div className="text-xs text-gray-400">Mentors</div>
                        </div>
                        <div className="space-y-1">
                          <Target className="w-4 h-4 text-purple-400 mx-auto" />
                          <div className="text-lg font-bold text-purple-400">{company.milestones.length}</div>
                          <div className="text-xs text-gray-400">Milestones</div>
                        </div>
                        <div className="space-y-1">
                          <Calendar className="w-4 h-4 text-emerald-400 mx-auto" />
                          <div className="text-lg font-bold text-emerald-400">
                            {new Date(company.joinDate).getFullYear()}
                          </div>
                          <div className="text-xs text-gray-400">Joined</div>
                        </div>
                      </div>
                    </div>
                  </GlassCard>
                </motion.div>

                {/* Company Details */}
                <motion.div
                  className="flex-1 space-y-6"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  <div>
                    <motion.h3
                      className="text-3xl lg:text-4xl font-bold text-white mb-2"
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                    >
                      {company.name}
                    </motion.h3>

                    <motion.div
                      className="flex items-center gap-3 mb-4"
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.5 }}
                    >
                      <span className="text-2xl">{company.programTrack.icon}</span>
                      <span className="text-purple-300 font-medium">{company.programTrack.name}</span>
                      <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
                        {company.currentStage}
                      </Badge>
                    </motion.div>
                  </div>

                  <motion.p
                    className="text-gray-300 text-lg leading-relaxed"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    {company.description}
                  </motion.p>

                  <motion.div
                    className="flex flex-col sm:flex-row gap-4"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <Link href={`/companies/${company.id}/instructions`} className="flex-1">
                      <AuthButton variant="primary" className="w-full group">
                        <BookOpen className="h-4 w-4 mr-2" />
                        View Company Guide
                        <ArrowRight className="h-4 w-4 ml-2 transition-transform group-hover:translate-x-1" />
                      </AuthButton>
                    </Link>

                    {company.website && (
                      <AuthButton
                        variant="outline"
                        className="flex-1 group"
                        onClick={() => window.open(company.website, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Visit Website
                        <ArrowRight className="h-4 w-4 ml-2 transition-transform group-hover:translate-x-1" />
                      </AuthButton>
                    )}
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Cosmic Call to Action */}
        <motion.div
          className="mt-24 relative"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Background cosmic elements */}
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: 6 }).map((_, i) => (
              <motion.div
                key={`cta-cosmic-${i}`}
                className="absolute w-1 h-1 bg-purple-400/40 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0, 2, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: Math.random() * 4,
                }}
              />
            ))}
          </div>

          <GlassCard className="p-12 text-center relative overflow-hidden">
            {/* Animated gradient overlay */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/5 to-blue-500/10"
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%', '0% 0%'],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "linear"
              }}
            />

            <div className="relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="flex items-center justify-center gap-2 mb-6">
                  <Rocket className="w-6 h-6 text-purple-400" />
                  <h2 className="text-3xl lg:text-4xl font-bold text-white">
                    Ready to Join Our{' '}
                    <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                      Cosmic Success Stories?
                    </span>
                  </h2>
                  <Star className="w-6 h-6 text-purple-400" />
                </div>
              </motion.div>

              <motion.p
                className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                Embark on your entrepreneurial journey through the cosmos of innovation.
                Transform your visionary ideas into stellar successes with our comprehensive incubation programs.
              </motion.p>

              <motion.div
                className="flex flex-col sm:flex-row gap-6 justify-center items-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <Link href="/programs">
                  <AuthButton variant="primary" className="group px-8 py-4 text-lg">
                    <Zap className="h-5 w-5 mr-2" />
                    Launch Your Journey
                    <ArrowRight className="h-5 w-5 ml-2 transition-transform group-hover:translate-x-1" />
                  </AuthButton>
                </Link>

                <Link href="/programs/portfolio">
                  <AuthButton variant="outline" className="group px-8 py-4 text-lg">
                    <BookOpen className="h-5 w-5 mr-2" />
                    Explore Portfolio
                    <ArrowRight className="h-5 w-5 ml-2 transition-transform group-hover:translate-x-1" />
                  </AuthButton>
                </Link>
              </motion.div>
            </div>
          </GlassCard>
        </motion.div>
        </div>
      </BokehBackground>
  );
}
